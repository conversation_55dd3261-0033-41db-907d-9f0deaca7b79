# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: dc27559385e905ad30838356c5f5d574014ba39872d732111cd07ac0beff4c57
      url: "https://pub.dev"
    source: hosted
    version: "80.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: a5788040810bd84400bc209913fbc40f388cded7cdf95ee2f5d2bff7e38d5241
      url: "https://pub.dev"
    source: hosted
    version: "1.3.58"
  adaptive_dialog:
    dependency: "direct main"
    description:
      name: adaptive_dialog
      sha256: "4cfd1567ba305c8df05499f2d6df8c19b0a20b31f35b1e58371f990eb9bfe987"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  agora_rtc_engine:
    dependency: "direct main"
    description:
      name: agora_rtc_engine
      sha256: f3b2cf4292692a4de45272adae79554f4942624af5049d222ac0dbe981944070
      url: "https://pub.dev"
    source: hosted
    version: "6.5.2"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "192d1c5b944e7e53b24b5586db760db934b177d4147c42fbca8c8c5f1eb8d11e"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  any_link_preview:
    dependency: transitive
    description:
      name: any_link_preview
      sha256: "0617bd49a58dd0478cd5c4c83bcf7a2d1b7d301aad229817d13693fe86807ea1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  app_badge_plus:
    dependency: transitive
    description:
      name: app_badge_plus
      sha256: "3b319d12719ecd4fd2c44099f1ce1df622eff7d958f3c99776b17313bfcf25e3"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  app_links:
    dependency: "direct main"
    description:
      name: app_links
      sha256: "85ed8fc1d25a76475914fff28cc994653bd900bc2c26e4b57a49e097febb54ba"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.0"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  appkit_ui_element_colors:
    dependency: transitive
    description:
      name: appkit_ui_element_colors
      sha256: c3e50f900aae314d339de489535736238627071457c4a4a2dbbb1545b4f04f22
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "7dcbd0f87fe5f61cb28da39a1a8b70dbc106e2fe0516f7836eb7bb2948481a12"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: e02d018628c870ef2d7f03e33f9ad179d89ff6ec52ca6c56bcb80bcef979867f
      url: "https://pub.dev"
    source: hosted
    version: "1.6.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  audio_service:
    dependency: "direct main"
    description:
      name: audio_service
      sha256: cb122c7c2639d2a992421ef96b67948ad88c5221da3365ccef1031393a76e044
      url: "https://pub.dev"
    source: hosted
    version: "0.18.18"
  audio_service_platform_interface:
    dependency: transitive
    description:
      name: audio_service_platform_interface
      sha256: "6283782851f6c8b501b60904a32fc7199dc631172da0629d7301e66f672ab777"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  audio_service_web:
    dependency: transitive
    description:
      name: audio_service_web
      sha256: b8ea9243201ee53383157fbccf13d5d2a866b5dda922ec19d866d1d5d70424df
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "8f96a7fecbb718cb093070f868b4cdcb8a9b1053dce342ff8ab2fde10eb9afb7"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  audio_waveforms:
    dependency: transitive
    description:
      name: audio_waveforms
      sha256: "658fef41bbab299184b65ba2fd749e8ec658c1f7d54a21f7cf97fa96b173b4ce"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: e653f162ddfcec1da2040ba2d8553fff1662b5c2a5c636f4c21a3b11bee497de
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: "60a6728277228413a85755bd3ffd6fab98f6555608923813ce383b190a360605"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "0811d6924904ca13f9ef90d19081e4a87f7297ddc19fc3d31f60af1aaafee333"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: f75bce1ce864170ef5e6a2c6a61cd3339e1a17ce11e99a25bae4474ea491d001
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "0e2f6a919ab56d0fec272e801abc07b26ae7f31980f912f24af4748763e5a656"
      url: "https://pub.dev"
    source: hosted
    version: "7.1.1"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "1c0f17cec68455556775f1e50ca85c40c05c714a99c5eb1d2d57cc17ba5522d7"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "4048797865105b26d47628e6abb49231ea5de84884160229251f37dfcbe52fd7"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  background_downloader:
    dependency: "direct main"
    description:
      name: background_downloader
      sha256: c3814aa0466368a4c1c32d24bc73ded752edacf06731ead43857a3ac992ed52d
      url: "https://pub.dev"
    source: hosted
    version: "8.9.5"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  blurhash:
    dependency: "direct overridden"
    description:
      name: blurhash
      sha256: d46ea05db7f5eceb9a1da6374e764adbdecb0b56bcd7e85d841496e51d880d5e
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  blurhash_dart:
    dependency: transitive
    description:
      name: blurhash_dart
      sha256: "43955b6c2e30a7d440028d1af0fa185852f3534b795cc6eb81fbf397b464409f"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: cef23f1eda9b57566c81e2133d196f8e3df48f244b317368d65c5943d91148f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "4ae2de3e1e67ea270081eaee972e1bd8f027d459f249e0f1186730784c2e7e33"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "8e928697a82be082206edb0b9c99c5a4ad6bc31c9e9b8b2f291ae65cd4a25daa"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: b9e4fda21d846e192628e7a4f6deda6888c36b5b69ba02ff291a01fd529140f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "058fe9dce1de7d69c4b84fada934df3e0153dd000758c4d65964d0166779aa99"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.15"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "22e3aa1c80e0ada3722fe5b63fd43d9c8990759d0a2cf489c8c5d7b2bdebc021"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: ea90e81dc4a25a043d9bee692d20ed6d1c4a1662a28c03a96417446c093ed6b4
      url: "https://pub.dev"
    source: hosted
    version: "8.9.5"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "7c1183e361e5c8b0a0f21a28401eecdbde252441106a9816400dd4c2b2424916"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "35814b016e37fbdc91f7ae18c8caf49ba5c88501813f73ce8a07027a395e2829"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "980842f4e8e2535b8dbd3d5ca0b1f0ba66bf61d14cc3a17a9b4788a3685ba062"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.dev"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: bd9737671a00d979e0310a946e5be2fdc621b6a36b95378755cb3e4498e61485
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10+2"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "6bff67a6821aa094fec2d317735c572264960a0afe034525ee355c4345524243"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.18+13"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "2f757024a48696ff4814a789b0bd90f5660c0fb25f393ab4564fb483327930e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  chewie:
    dependency: transitive
    description:
      name: chewie
      sha256: df6711bc3ba165ad19cb496e350250be5673327f79c61c9cc8a15088ed8007ed
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  chopper:
    dependency: "direct main"
    description:
      name: chopper
      sha256: "18928a74069cf1c257e657809c1b84b0304d8e32a6fc446dd2bbb28657e0358a"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  chopper_generator:
    dependency: "direct dev"
    description:
      name: chopper_generator
      sha256: "2cb23febc9ac9bbc9288b4703e60f3edc8e301a2ef5057d0c22369eab7313c6d"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "04bf81bb0b77de31557b58d052b24b3eee33f09a6e7a8c68a3e247c7df19ec27"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.3"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dart_openai:
    dependency: "direct main"
    description:
      name: dart_openai
      sha256: "853bb57fed6a71c3ba0324af5cb40c16d196cf3aa55b91d244964ae4a241ccf1"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "27eb0ae77836989a3bc541ce55595e8ceee0992807f14511552a898ddd0d88ac"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  dashed_circle:
    dependency: "direct main"
    description:
      name: dashed_circle
      sha256: "****************************************8b7f224906fe52e1be5c84f8"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  desktop_notifications:
    dependency: transitive
    description:
      name: desktop_notifications
      sha256: "6d92694ad6e9297a862c5ff7dd6b8ff64c819972557754769f819d2209612927"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.3"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: "306b78788d1bb569edb7c55d622953c2414ca12445b41c9117963e03afc5c513"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.3"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: "12981945ec38931748836cd76f2b38773118d0baef3c68404bdfde9566147876"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  dynamic_color:
    dependency: transitive
    description:
      name: dynamic_color
      sha256: eae98052fa6e2826bdac3dd2e921c6ce2903be15c6b7f8b6d8a5d49b5086298d
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  email_validator:
    dependency: "direct main"
    description:
      name: email_validator
      sha256: b19aa5d92fdd76fbc65112060c94d45ba855105a28bb6e462de7ff03b12fa1fb
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  emoji_picker_flutter:
    dependency: transitive
    description:
      name: emoji_picker_flutter
      sha256: "08567e6f914d36c32091a96cf2f51d2558c47aa2bd47a590dc4f50e42e0965f6"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  enum_to_string:
    dependency: "direct main"
    description:
      name: enum_to_string
      sha256: "93b75963d3b0c9f6a90c095b3af153e1feccb79f6f08282d3274ff8d9eea52bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  eraser:
    dependency: transitive
    description:
      name: eraser
      sha256: a91db34b0f822b30093f94f9482c781392fc66c2cf6ed3295487313efd3d2da0
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  event_bus_plus:
    dependency: transitive
    description:
      name: event_bus_plus
      sha256: a8381b29384d04ccd742e53668bc8b3ea99ee80496b26adf23fcceba8cdeb673
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  fc_native_video_thumbnail:
    dependency: transitive
    description:
      name: fc_native_video_thumbnail
      sha256: "61836a6fd34bb0cbda48d7ba7cd7a23242468886d4c68017ae59b9791fb42d2a"
      url: "https://pub.dev"
    source: hosted
    version: "0.16.1"
  fetch_api:
    dependency: transitive
    description:
      name: fetch_api
      sha256: "24cbd5616f3d4008c335c197bb90bfa0eb43b9e55c6de5c60d1f805092636034"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  fetch_client:
    dependency: transitive
    description:
      name: fetch_client
      sha256: "375253f4efe64303c793fb17fe90771c591320b2ae11fb29cb5b406cc8533c00"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: transitive
    description:
      name: file_picker
      sha256: ab13ae8ef5580a411c458d6207b6774a6c237d77ac37011b13994879f68a8810
      url: "https://pub.dev"
    source: hosted
    version: "8.3.7"
  file_saver:
    dependency: transitive
    description:
      name: file_saver
      sha256: "017a127de686af2d2fbbd64afea97052d95f2a0f87d19d25b87e097407bf9c1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.14"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+4"
  file_sizes:
    dependency: "direct main"
    description:
      name: file_sizes
      sha256: d24964a4b194b6116d490005428d07cb3e83834ad1f7ec6a1012dedc2f6d2a19
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: c6e8a6bf883d8ddd0dec39be90872daca65beaa6f4cff0051ed3b16c56b82e9f
      url: "https://pub.dev"
    source: hosted
    version: "3.15.1"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "5dbc900677dcbe5873d22ad7fbd64b047750124f1f9b7ebe2a33b9ddccc838eb"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "0ed0dc292e8f9ac50992e2394e9d336a0275b6ae400d64163fdf0a8a8b556c37"
      url: "https://pub.dev"
    source: hosted
    version: "2.24.1"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "0f3363f97672eb9f65609fa00ed2f62cc8ec93e7e2d4def99726f9165d3d8a73"
      url: "https://pub.dev"
    source: hosted
    version: "15.2.9"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "7a05ef119a14c5f6a9440d1e0223bcba20c8daf555450e119c4c477bf2c3baa9"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.9"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: a4547f76da2a905190f899eb4d0150e1d0fd52206fce469d9f05ae15bb68b2c5
      url: "https://pub.dev"
    source: hosted
    version: "3.10.9"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_advanced_avatar:
    dependency: transitive
    description:
      name: flutter_advanced_avatar
      sha256: c97fa65a2499d85f367421b8386d45ce090046dcf24f6f71042e5e4e5c6a8c59
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "5e67678e479ac639069d7af1e133f4a4702311491188ff3e0227486430db0c06"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.2"
  flutter_cache_manager:
    dependency: "direct main"
    description:
      name: flutter_cache_manager
      sha256: "400b6592f16a4409a7f2bb929a9a7e38c72cceb8ffb99ee57bbf2cb2cecf8386"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  flutter_callkit_incoming:
    dependency: transitive
    description:
      path: "."
      ref: dev
      resolved-ref: "2f74feb49f61441ddc678bb43afd2c6e8a208c82"
      url: "https://github.com/hatemragab/flutter_callkit_incoming.git"
    source: git
    version: "2.0.4+2"
  flutter_chat_types:
    dependency: transitive
    description:
      name: flutter_chat_types
      sha256: e285b588f6d19d907feb1f6d912deaf22e223656769c34093b64e1c59b094fb9
      url: "https://pub.dev"
    source: hosted
    version: "3.6.2"
  flutter_dropzone:
    dependency: transitive
    description:
      name: flutter_dropzone
      sha256: "032966dfb104180935fd05d83c1fd3e10b83670611fbd75fb1578c65ae788817"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  flutter_dropzone_platform_interface:
    dependency: "direct overridden"
    description:
      name: flutter_dropzone_platform_interface
      sha256: b4e2df75364bab2f4c3ca32b87193267d786b6e158c3f345c3a1daeb911d82b9
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  flutter_dropzone_web:
    dependency: "direct overridden"
    description:
      name: flutter_dropzone_web
      sha256: e852e07fd216969a1b0d9add717d27ec2b08e7c7d2bfc3ba8c06dc38e956dd26
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  flutter_fgbg:
    dependency: transitive
    description:
      name: flutter_fgbg
      sha256: e02ad0738ba5fc7f331b62acb0d74aa540626a6441ae18fad685faa5ac4ad7a5
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_image_compress:
    dependency: transitive
    description:
      name: flutter_image_compress
      sha256: "51d23be39efc2185e72e290042a0da41aed70b14ef97db362a6b5368d0523b27"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: c5c5d50c15e97dd7dc72ff96bd7077b9f791932f2076c5c5b6c43f2c88607bfb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "20019719b71b743aba0ef874ed29c50747461e5e8438980dfa5c2031898f7337"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: e76b92bbc830ee08f5b05962fc78a532011fcd2041f620b5400a593e96da3f51
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: b9b141ac7c686a2ce7bb9a98176321e1182c9074650e47bb140741a44b6f5a96
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: bfa04787c85d80ecb3f8777bde5fc10c3de809240c48fa061a2c2bf15ea5211c
      url: "https://pub.dev"
    source: hosted
    version: "0.14.3"
  flutter_link_previewer:
    dependency: transitive
    description:
      name: flutter_link_previewer
      sha256: "007069e60f42419fb59872beb7a3cc3ea21e9f1bdff5d40239f376fa62ca9f20"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  flutter_linkify:
    dependency: transitive
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "5398f14efa795ffb7a33e9b6a08798b26a180edac4ad7db3f231e40f82ce11e1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: ef41ae901e7529e52934feba19ed82827b11baa67336829564aeab3129460610
      url: "https://pub.dev"
    source: hosted
    version: "18.0.1"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "8f685642876742c941b29c32030f6f4f6dacd0e4eaecb3efbb187d6a3812ca01"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "6c5b83c86bf819cdb177a9247a3722067dd8cc6313827ce7c77a4b238a26fd52"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: "direct main"
    description:
      name: flutter_native_splash
      sha256: "8321a6d11a8d13977fa780c89de8d257cce3d841eecfb7a4cadffcc4f12d82dc"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.6"
  flutter_painter_v2:
    dependency: transitive
    description:
      name: flutter_painter_v2
      sha256: bdbe07344e38d7c8b0d6fd6ea056ad7f20b5e78b79a3c3ca4182951d0436a3ff
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0+1"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      sha256: "529cf5793b7acdf16ee0f97b158d0d4ba0bf06e7121ef180abe1a5b59e32c1e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "5a1e6fb2c0561958d7e4c33574674bda7b77caaca7a33b758876956f2902eea3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.27"
  flutter_ringtone_player:
    dependency: transitive
    description:
      name: flutter_ringtone_player
      sha256: ae4a2caab2cfd14902f3ee5fe0307c454b396f0077fa34700238d6c4bd99cfec
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0+4"
  flutter_smart_dialog:
    dependency: transitive
    description:
      name: flutter_smart_dialog
      sha256: a3aaf690b2737ee6b2c7e7a983bc685e5f118e5de7e2042d2e0b7db26eb074f2
      url: "https://pub.dev"
    source: hosted
    version: "4.9.8+7"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: c200fd79c918a40c5cd50ea0877fa13f81bdaf6f0a5d3dbcc2a13e3285d6aa1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_vlc_player:
    dependency: transitive
    description:
      name: flutter_vlc_player
      sha256: c2554ae0de8133ff5c6d174381f8097f482cdb51ea5aa75d03ee139b71490b53
      url: "https://pub.dev"
    source: hosted
    version: "7.4.3"
  flutter_vlc_player_platform_interface:
    dependency: transitive
    description:
      name: flutter_vlc_player_platform_interface
      sha256: "99fbb806f86ce1c53ba007157c183104a100aa643eda0cec7e71118bf0460578"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  from_css_color:
    dependency: transitive
    description:
      name: from_css_color
      sha256: "5e4d1795c8d10af94e51dd97636b2a29170a132be1aceba103e9866028d20823"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  gal:
    dependency: transitive
    description:
      name: gal
      sha256: "2771519c8b29f784d5e27f4efc2667667eef51c6c47cccaa0435a8fe8aa208e4"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: "149876cc5207a0f5daf4fdd3bfcf0a0f27258b3fe95108fa084f527ad0568f1b"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.0"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: fcb1760a50d7500deca37c9a666785c047139b5f9ee15aa5469fae7dbbe3170d
      url: "https://pub.dev"
    source: hosted
    version: "4.6.2"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: dbdd8789d5aaf14cf69f74d4925ad1336b4433a6efdf2fce91e8955dc921bf22
      url: "https://pub.dev"
    source: hosted
    version: "2.3.13"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "30cb64f0b9adcc0fb36f628b4ebf4f731a2961a0ebd849f4b56200205056fe67"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.6"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: b1ae9bdfd90f861fde8fd4f209c37b953d65e92823cb73c7dee1fa021b06f172
      url: "https://pub.dev"
    source: hosted
    version: "4.1.3"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "175435404d20278ffd220de83c2ca293b73db95eafbdc8131fe8609be1421eb6"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: f126a3e286b7f5b578bf436d5592968706c4c1de28a228b870ce375d9f743103
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  google_api_availability:
    dependency: transitive
    description:
      name: google_api_availability
      sha256: "2ffdc91e1e0cf4e7974fef6c2988a24cefa81f03526ff04b694df6dc0fcbca03"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  google_api_availability_android:
    dependency: transitive
    description:
      name: google_api_availability_android
      sha256: "4794147f43a8f3eee6b514d3ae30dbe6f7b9048cae8cd2a74cb4055cd28d74a8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  google_api_availability_platform_interface:
    dependency: transitive
    description:
      name: google_api_availability_platform_interface
      sha256: "65b7da62fe5b582bb3d508628ad827d36d890710ea274766a992a56fa5420da6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: b1ac0fe2832c9cc95e5e88b57d627c5e68c223b9657f4b96e1487aa9098c7b82
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: transitive
    description:
      name: google_maps_flutter
      sha256: e1805e5a5885bd14a1c407c59229f478af169bf4d04388586b19f53145a5db3a
      url: "https://pub.dev"
    source: hosted
    version: "2.12.3"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "356ee9c65f38a104f7c4988e6952e52addb3b6cb1601839dd2010d7a502afcf0"
      url: "https://pub.dev"
    source: hosted
    version: "2.16.2"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: d03678415da9de8ce7208c674b264fc75946f326e696b4b7f84c80920fc58df6
      url: "https://pub.dev"
    source: hosted
    version: "2.15.4"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: f8293f072ed8b068b092920a72da6693aa8b3d62dc6b5c5f0bc44c969a8a776c
      url: "https://pub.dev"
    source: hosted
    version: "2.12.1"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: ce2cac714e5462bf761ff2fdfc3564c7e5d7ed0578268dccb0a54dbdb1e6214e
      url: "https://pub.dev"
    source: hosted
    version: "0.5.12+2"
  google_mobile_ads:
    dependency: "direct main"
    description:
      name: google_mobile_ads
      sha256: "0d4a3744b5e8ed1b8be6a1b452d309f811688855a497c6113fc4400f922db603"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.1"
  gradient_borders:
    dependency: transitive
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  hexcolor:
    dependency: "direct main"
    description:
      name: hexcolor
      sha256: c07f4bbb9095df87eeca87e7c69e8c3d60f70c66102d7b8d61c4af0453add3f6
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "9475be233c437f0e3637af55e7702cbbe5c23a68bd56e8a5fa2d426297b7c6c8"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.5+1"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: fe7ab022b76f3034adc518fb6ea04a82387620e19977665ea18d30a1cf43442f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "4e973fcf4caae1a4be2fa0a13157aa38a8f9cb049db6529aa00b4d71abc4d928"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.4"
  image_cropper:
    dependency: transitive
    description:
      name: image_cropper
      sha256: "266760ed426d7121f0ada02c672bfe5c1b5c714e908328716aee756f045709dc"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: fd81ebe36f636576094377aab32673c4e5d1609b32dec16fad98d2b71f1250a9
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: "6ca6b81769abff9a4dcc3bbd3d75f5dfa9de6b870ae9613c8cd237333a4283af"
      url: "https://pub.dev"
    source: hosted
    version: "7.1.0"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8bd392ba8b0c8957a157ae0dc9fcf48c58e6c20908d5880aea1d79734df090e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+22"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "34a65f6740df08bbbeb0a1abd8e6d32107941fd4868f67a507b25601651022c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  in_app_purchase:
    dependency: "direct main"
    description:
      name: in_app_purchase
      sha256: "5cddd7f463f3bddb1d37a72b95066e840d5822d66291331d7f8f05ce32c24b6c"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  in_app_purchase_android:
    dependency: transitive
    description:
      name: in_app_purchase_android
      sha256: fd76e5612da6facadcfe8a3477da092908227260a9f6ec7db9a66dd989c69b02
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  in_app_purchase_platform_interface:
    dependency: transitive
    description:
      name: in_app_purchase_platform_interface
      sha256: "1d353d38251da5b9fea6635c0ebfc6bb17a2d28d0e86ea5e083bf64244f1fb4c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  in_app_purchase_storekit:
    dependency: transitive
    description:
      name: in_app_purchase_storekit
      sha256: a9bc29f5e67701192cc6ea2c4dc99efc1f25fcdc63e052af9b271d479626319b
      url: "https://pub.dev"
    source: hosted
    version: "0.4.3"
  intersperse:
    dependency: transitive
    description:
      name: intersperse
      sha256: "2f8a905c96f6cbba978644a3d5b31b8d86ddc44917662df7d27a61f3df66a576"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.2"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  iris_method_channel:
    dependency: transitive
    description:
      name: iris_method_channel
      sha256: "99143ae77073ee8fd6c19d8f56ec59ba6c332000d8bea46bb9b6a89d83feca5c"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "53385261521cc4a0c4658fd0ad07a7d14591cf8fc33abbceae306ddb974888dc"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  just_audio:
    dependency: transitive
    description:
      name: just_audio
      sha256: "679637a3ec5b6e00f36472f5a3663667df00ee4822cbf5dafca0f568c710960a"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.4"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "4cd94536af0219fa306205a58e78d67e02b0555283c1c094ee41e402a14a5c4a"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "8c7e779892e180cbc9ffb5a3c52f6e90e1cbbf4a63694cc450972a7edbd2bb6d"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.15"
  latlong2:
    dependency: transitive
    description:
      name: latlong2
      sha256: "98227922caf49e6056f91b6c56945ea1c7b166f28ffcd5fb8e72fc0b453cc8fe"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: c35bb79562d980e9a453fc715854e1ed39e24e7d0297a880ef54e17f9874a9d7
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  loadmore:
    dependency: "direct main"
    description:
      name: loadmore
      sha256: "4a9d60cf1182f9a91449502b4ab2bad53aaf292124382c7e29e36a61468b027f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: "direct main"
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  macos_ui:
    dependency: transitive
    description:
      name: macos_ui
      sha256: "204a52eed576e38099037401c092cb3d7ab41193e3ed0ba5ac2c585b3cf755a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.10"
  macos_window_utils:
    dependency: transitive
    description:
      name: macos_window_utils
      sha256: "28ebb4e6d5cbaa1330b5f78d13cf9693779830d0ffc64b471f8d384dd73b7b21"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.1"
  map_launcher:
    dependency: transitive
    description:
      name: map_launcher
      sha256: "7436d6ef9ae57ff15beafcedafe0a8f0604006cbecd2d26024c4cfb0158c2b9a"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: "41a20518f0cb1256669420fdba0cd90d21561e560ac240f26ef8322e45bb7ed6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  mime_type:
    dependency: transitive
    description:
      name: mime_type
      sha256: d652b613e84dac1af28030a9fba82c0999be05b98163f9e18a0849c6e63838bb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  modal_bottom_sheet:
    dependency: "direct main"
    description:
      name: modal_bottom_sheet
      sha256: eac66ef8cb0461bf069a38c5eb0fa728cee525a531a8304bd3f7b2185407c67e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  objectid:
    dependency: transitive
    description:
      name: objectid
      sha256: a6ee03b89818bfe210966d5bbc00ad5b19172298e5c2671c9c822a394c8339d8
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  oktoast:
    dependency: transitive
    description:
      name: oktoast
      sha256: f1366c5c793ddfb8f55bc6fc3e45db43c45debf173b765fb4c5ec096cbdeb84a
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  open_filex:
    dependency: transitive
    description:
      name: open_filex
      sha256: "9976da61b6a72302cf3b1efbce259200cd40232643a467aac7370addf94d6900"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7976bfe4c583170d6cdc7077e3237560b364149fcd268b5f53d95a991963b191"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "6c935fb612dff8e3cc9632c2b301720c77450a126114126ffaafe28d2e87956c"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  pasteboard:
    dependency: transitive
    description:
      name: pasteboard
      sha256: "7bf733f3a00c7188ec1f2c6f0612854248b302cf91ef3611a2b7bb141c0f9d55"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "0ca7359dad67fd7063cb2892ab0c0737b2daafd807cf1acecd62374c8fae6c12"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.16"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  percent_indicator:
    dependency: transitive
    description:
      name: percent_indicator
      sha256: "157d29133bbc6ecb11f923d36e7960a96a3f28837549a20b65e5135729f0f9fd"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.5"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f84a188e79a35c687c132a0a0556c254747a08561e99ab933f12f6ca71ef3c98
      url: "https://pub.dev"
    source: hosted
    version: "9.4.6"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  phosphor_flutter:
    dependency: transitive
    description:
      name: phosphor_flutter
      sha256: "8a14f238f28a0b54842c5a4dc20676598dd4811fcba284ed828bd5a262c11fde"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: "0bc7548fd3111eb93a3b0abf1c57364e40aeda32512c100085a48dade60e574f"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.4"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b6015b67b32f345f57cf32c126f871bced2501236c405aafaefa885f7c821e4f
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.0"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: "8a73be426a91fefec90a7f130763ca39772d547e92f19a827cf4aa02e323d35a"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  place_picker_v2:
    dependency: transitive
    description:
      name: place_picker_v2
      sha256: cdd2dc09330eb4e495926a5dc5da67f759b4d6a24a61d6965d518726d90c71bf
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  platform_local_notifications:
    dependency: transitive
    description:
      path: "../../packages/platform_local_notifications"
      relative: true
    source: path
    version: "1.0.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  posix:
    dependency: transitive
    description:
      name: posix
      sha256: a0117dc2167805aa9125b82eee515cc891819bac2f538c83646d355b16f58b9a
      url: "https://pub.dev"
    source: hosted
    version: "6.0.1"
  pro_image_editor:
    dependency: transitive
    description:
      name: pro_image_editor
      sha256: "27190b0333af71e9949f366ac303496511ef6d67607f6f9797c9f136371a321f"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "489024f942069c2920c844ee18bb3d467c69e48955a4f32d1677f71be103e310"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "0560ba233314abbed0a48a2956f7f022cce7c3e1e73df540277da7544cad4082"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  qs_dart:
    dependency: transitive
    description:
      name: qs_dart
      sha256: c775dbe663cd59365050220b3499dee259b72ad6b352a3e087a15bd77e161b74
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3+1"
  quick_notify:
    dependency: transitive
    description:
      path: "../../packages/platform_local_notifications/quick_notify"
      relative: true
    source: path
    version: "0.3.0-dev.0"
  record:
    dependency: transitive
    description:
      name: record
      sha256: "4a5cf4d083d1ee49e0878823c4397d073f8eb0a775f31215d388e2bc47a9e867"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  record_android:
    dependency: transitive
    description:
      name: record_android
      sha256: "36e009c3b83e034321a44a7683d95dd055162a231f95600f7da579dcc79701f9"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  record_darwin:
    dependency: transitive
    description:
      name: record_darwin
      sha256: e487eccb19d82a9a39cd0126945cfc47b9986e0df211734e2788c95e3f63c82c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  record_linux:
    dependency: transitive
    description:
      name: record_linux
      sha256: "74d41a9ebb1eb498a38e9a813dd524e8f0b4fdd627270bda9756f437b110a3e3"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  record_platform_interface:
    dependency: transitive
    description:
      name: record_platform_interface
      sha256: "8a575828733d4c3cb5983c914696f40db8667eab3538d4c41c50cbb79e722ef4"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  record_web:
    dependency: transitive
    description:
      name: record_web
      sha256: "654c08113961051dcb5427e63f56315ba47c0752781ba990dac9313d0ec23c70"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  record_windows:
    dependency: transitive
    description:
      name: record_windows
      sha256: "26bfebc8899f4fa5b6b044089887dc42115820cd6a907bdf40c16e909e87de0a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  recursive_regex:
    dependency: transitive
    description:
      name: recursive_regex
      sha256: f7252e3d3dfd1665e594d9fe035eca6bc54139b1f2fee38256fa427ea41adc60
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  responsive_builder:
    dependency: "direct main"
    description:
      name: responsive_builder
      sha256: "64a5ef3fbe3628e4588a0c2391c3186300e76f58621d8135cc77aac816255a3e"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  responsive_framework:
    dependency: "direct main"
    description:
      name: responsive_framework
      sha256: a8e1c13d4ba980c60cbf6fa1e9907cd60662bf2585184d7c96ca46c43de91552
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  s_translation:
    dependency: "direct main"
    description:
      path: "../../packages/s_translation"
      relative: true
    source: path
    version: "0.0.1"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  screen_retriever:
    dependency: transitive
    description:
      name: screen_retriever
      sha256: "570dbc8e4f70bac451e0efc9c9bb19fa2d6799a11e6ef04f946d7886d2e23d0c"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  screen_retriever_linux:
    dependency: transitive
    description:
      name: screen_retriever_linux
      sha256: f7f8120c92ef0784e58491ab664d01efda79a922b025ff286e29aa123ea3dd18
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  screen_retriever_macos:
    dependency: transitive
    description:
      name: screen_retriever_macos
      sha256: "71f956e65c97315dd661d71f828708bd97b6d358e776f1a30d5aa7d22d78a149"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  screen_retriever_platform_interface:
    dependency: transitive
    description:
      name: screen_retriever_platform_interface
      sha256: ee197f4581ff0d5608587819af40490748e1e39e648d7680ecf95c05197240c0
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  screen_retriever_windows:
    dependency: transitive
    description:
      name: screen_retriever_windows
      sha256: "449ee257f03ca98a57288ee526a301a430a344a161f9202b4fcc38576716fe13"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  sensors_plus:
    dependency: transitive
    description:
      name: sensors_plus
      sha256: "905282c917c6bb731c242f928665c2ea15445aa491249dea9d98d7c79dc8fd39"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: "58815d2f5e46c0c41c40fb39375d3f127306f7742efe3b891c0b1c87e2b5cd5d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  share_handler:
    dependency: transitive
    description:
      name: share_handler
      sha256: "76575533be04df3fecbebd3c5b5325a8271b5973131f8b8b0ab8490c395a5d37"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.22"
  share_handler_android:
    dependency: transitive
    description:
      name: share_handler_android
      sha256: "124dcc914fb7ecd89076d3dc28435b98fe2129a988bf7742f7a01dcb66a95667"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  share_handler_ios:
    dependency: transitive
    description:
      name: share_handler_ios
      sha256: cdc21f88f336a944157a8e9ceb191525cee3b082d6eb6c2082488e4f09dc3ece
      url: "https://pub.dev"
    source: hosted
    version: "0.0.15"
  share_handler_platform_interface:
    dependency: transitive
    description:
      name: share_handler_platform_interface
      sha256: "7a4df95a87b326b2f07458d937f2281874567c364b7b7ebe4e7d50efaae5f106"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: fce43200aa03ea87b91ce4c3ac79f0cecd52e2a7a56c7a4185023c271fbfa6da
      url: "https://pub.dev"
    source: hosted
    version: "10.1.4"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: cc012a23fc2d479854e6c80150696c4a5f5bb62cb89af4de1c505cf78d0a5d0b
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "6e8bf70b7fef813df4e9a36f658ac46d107db4b4cfe1048b477d4e453a8159f5"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: c2c8c46297b5d6a80bed7741ec1f2759742c77d272f1a1698176ae828f8e1a18
      url: "https://pub.dev"
    source: hosted
    version: "2.4.9"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: e7dd780a7ffb623c57850b33f43309312fc863fb6aa3d276a754bb299839ef12
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "3632775c8e90d6c9712f883e633716432a27758216dfb61bd86a8321c0580925"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  socket_io_client:
    dependency: transitive
    description:
      name: socket_io_client
      sha256: "3267c0985c138bfa5c28f6f22ae6d70b04e26bd8e4a850b2400479374aa37174"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  socket_io_common:
    dependency: transitive
    description:
      name: socket_io_common
      sha256: "162fbaecbf4bf9a9372a62a341b3550b51dcef2f02f3e5830a297fd48203d45b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "35c8150ece9e8c8d263337a265153c3329667640850b9304861faea59fc98f6b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: e2297b1da52f127bc7a3da11439985d9b536f75070f3325e62ada69a5c585d03
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "2b3070c5fa881839f8b402ee4a39c1b4d561704d4ebbbcfb808a119bc2a1701b"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "84731e8bfd8303a3389903e01fb2141b6e59b5973cacbb0929021df08dddbe8b"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.5"
  sqflite_common_ffi:
    dependency: transitive
    description:
      name: sqflite_common_ffi
      sha256: "1f3ef3888d3bfbb47785cc1dda0dc7dd7ebd8c1955d32a9e8e9dae1e38d1c4c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.5"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "279832e5cde3fe99e8571879498c9211f3ca6391b0d818df4e17d9fff5c6ccb3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqlite3:
    dependency: transitive
    description:
      name: sqlite3
      sha256: "310af39c40dd0bb2058538333c9d9840a2725ae0b9f77e4fd09ad6696aa8f66e"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.5"
  sqlite3_flutter_libs:
    dependency: transitive
    description:
      name: sqlite3_flutter_libs
      sha256: "1a96b59227828d9eb1463191d684b37a27d66ee5ed7597fcf42eee6452c88a14"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.32"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  stop_watch_timer:
    dependency: transitive
    description:
      name: stop_watch_timer
      sha256: "9128717892ac94094e61544cb5259aff79eefebc0c4549a46d815689c56be8a7"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  story_view:
    dependency: "direct main"
    description:
      name: story_view
      sha256: ab64063569178e405b643ab11bdc28d6e85333191e3c8b5556e864fe6c5b2a5a
      url: "https://pub.dev"
    source: hosted
    version: "0.16.5"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  string_validator:
    dependency: transitive
    description:
      name: string_validator
      sha256: a278d038104aa2df15d0e09c47cb39a49f907260732067d0034dc2f2e4e2ac94
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  super_up_core:
    dependency: "direct main"
    description:
      path: "../../packages/super_up_core"
      relative: true
    source: path
    version: "0.0.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "0669c70faae6270521ee4f05bffd2919892d42d1276e6c495be80174b6bc0ef6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  textless:
    dependency: transitive
    description:
      path: "."
      ref: HEAD
      resolved-ref: "5390ab6b6471b191e56f7f211531d5a4743d20f6"
      url: "https://github.com/hatemragab/textless.git"
    source: git
    version: "6.6.6"
  timeago:
    dependency: transitive
    description:
      name: timeago
      sha256: b05159406a97e1cbb2b9ee4faa9fb096fe0e2dfcd8b08fcd2a00553450d3422e
      url: "https://pub.dev"
    source: hosted
    version: "3.7.1"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: ffc9d5f4d1193534ef051f9254063fa53d588609418c84299956c3db9383587d
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  touchable_opacity:
    dependency: "direct main"
    description:
      name: touchable_opacity
      sha256: b3c9869c1e2f8b6fed597b3ca6a8ed68d409b8e161e56723e7ccab8ccf99a141
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "1d0eae19bd7606ef60fe69ef3b312a437a16549476c42321d5dc1506c9ca3bf4"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.15"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "3ba963161bd0fe395917ba881d320b9c4f6dd3c4a233da62ab18a5025c85f1e9"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  url_strategy:
    dependency: "direct main"
    description:
      name: url_strategy
      sha256: "6eff69fa0900b731a23552b38b54389f399d247dbb0998f2cbdf25bef6790a7c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  v_chat_firebase_fcm:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_firebase_fcm"
      relative: true
    source: path
    version: "1.2.1"
  v_chat_input_ui:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_input_ui"
      relative: true
    source: path
    version: "1.2.1"
  v_chat_media_editor:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_media_editor"
      relative: true
    source: path
    version: "1.2.2"
  v_chat_mention_controller:
    dependency: transitive
    description:
      name: v_chat_mention_controller
      sha256: "953ff4e4744b91ec09f8a667b860a52e86a8df819ecef938498ed6e11c4fa7f1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  v_chat_message_page:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_message_page"
      relative: true
    source: path
    version: "1.1.0"
  v_chat_receive_share:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_receive_share"
      relative: true
    source: path
    version: "1.3.0"
  v_chat_room_page:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_room_page"
      relative: true
    source: path
    version: "1.1.0"
  v_chat_sdk_core:
    dependency: "direct main"
    description:
      path: "../../packages/v_chat_sdk_core"
      relative: true
    source: path
    version: "1.1.0"
  v_chat_voice_player:
    dependency: transitive
    description:
      name: v_chat_voice_player
      sha256: "3960033992e87f569cbfd1c82fc812c5139126625907c6b4f57d0d1f1d3e23a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  v_platform:
    dependency: "direct main"
    description:
      name: v_platform
      sha256: db572561acbc26fa360ca408d736fdf25e759e4cc868c063dce3cdc374255e40
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  version:
    dependency: "direct main"
    description:
      name: version
      sha256: "3d4140128e6ea10d83da32fef2fa4003fccbf6852217bb854845802f04191f94"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  vibration:
    dependency: transitive
    description:
      name: vibration
      sha256: "3b08a0579c2f9c18d5d78cb5c74f1005f731e02eeca6d72561a2e8059bf98ec3"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  vibration_platform_interface:
    dependency: transitive
    description:
      name: vibration_platform_interface
      sha256: "6ffeee63547562a6fef53c05a41d4fdcae2c0595b83ef59a4813b0612cd2bc36"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: "7d78f0cfaddc8c19d4cb2d3bebe1bfef11f2103b0a03e5398b303a1bf65eeb14"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: ae7d4f1b41e3ac6d24dd9b9d5d6831b52d74a61bdd90a7a6262a33d8bb97c29a
      url: "https://pub.dev"
    source: hosted
    version: "2.8.2"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "84b4752745eeccb6e75865c9aab39b3d28eb27ba5726d352d45db8297fbd75bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: df534476c341ab2c6a835078066fc681b8265048addd853a1e3c78740316a844
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "3ef40ea6d72434edbfdba4624b90fd3a80a0740d260667d91e7ecd2d79e13476"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  volume_control:
    dependency: "direct main"
    description:
      name: volume_control
      sha256: "2c370083dd3892b8e5f5650275e452fb97e90a1e1abff65b5f6a761e2bc8b754"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: b90fbcc8d7bdf3b883ea9706d9d76b9978cb1dfa4351fcc8014d6ec31a493354
      url: "https://pub.dev"
    source: hosted
    version: "1.2.11"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "70e780bc99796e1db82fe764b1e7dcb89a86f1e5b3afb1db354de50f2e41eb7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  weak_map:
    dependency: transitive
    description:
      name: weak_map
      sha256: "5f8e5d5ce57dc624db5fae814dd689ccae1f17f92b426e52f0a7cbe7f6f4ab97"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web_socket:
    dependency: transitive
    description:
      name: web_socket
      sha256: "3c12d96c0c9a4eec095246debcea7b86c0324f22df69893d538fcc6f1b8cce83"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "0b8e2457400d8a859b7b2030786835a28a8e80836ef64402abef392ff4f1d0e5"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: e09150b28a07933839adef0e4a088bb43e8c8d9e6b93025b01882d4067a58ab0
      url: "https://pub.dev"
    source: hosted
    version: "4.3.4"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: c14455137ce60a68e1ccaf4e8f2dae8cebcb3465ddaa2fcfb57584fb7c5afe4d
      url: "https://pub.dev"
    source: hosted
    version: "3.18.5"
  wechat_camera_picker:
    dependency: transitive
    description:
      name: wechat_camera_picker
      sha256: "431cb1fdc3440156416af9ab29e5633b605483124ec8f8ca9905b163aa2cc5ac"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.7"
  wechat_picker_library:
    dependency: transitive
    description:
      name: wechat_picker_library
      sha256: a42e09cb85b15fc9410f6a69671371cc60aa99c4a1f7967f6593a7f665f6f47a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: dc6ecaa00a7c708e5b4d10ee7bec8c270e9276dfcab1783f57e9962d7884305f
      url: "https://pub.dev"
    source: hosted
    version: "5.12.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "6f1b564492d0147b330dd794fee8f512cec4977957f310f9951b5f9d83618dae"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  window_manager:
    dependency: "direct main"
    description:
      name: window_manager
      sha256: "732896e1416297c63c9e3fb95aea72d0355f61390263982a47fd519169dc5059"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.3"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: "direct main"
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
sdks:
  dart: ">=3.7.0 <4.0.0"
  flutter: ">=3.27.0"
